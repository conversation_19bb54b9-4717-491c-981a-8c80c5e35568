"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateOrganizationSchema = exports.createOrganizationSchema = exports.refreshTokenSchema = exports.changePasswordSchema = exports.registerSchema = exports.loginSchema = exports.idSchema = exports.paginationSchema = void 0;
const joi_1 = __importDefault(require("joi"));
// Common schemas
exports.paginationSchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    limit: joi_1.default.number().integer().min(1).max(100).default(10),
    sortBy: joi_1.default.string().optional(),
    sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
});
exports.idSchema = joi_1.default.object({
    id: joi_1.default.string().required(),
});
// Auth schemas
exports.loginSchema = joi_1.default.object({
    email: joi_1.default.string().email().required(),
    password: joi_1.default.string().min(6).required(),
});
exports.registerSchema = joi_1.default.object({
    email: joi_1.default.string().email().required(),
    password: joi_1.default.string().min(8).required(),
    firstName: joi_1.default.string().min(2).max(50).required(),
    lastName: joi_1.default.string().min(2).max(50).required(),
    organizationId: joi_1.default.string().required(),
    role: joi_1.default.string().valid('ADMIN', 'MANAGER', 'AGENT', 'VIEWER').default('AGENT'),
});
exports.changePasswordSchema = joi_1.default.object({
    currentPassword: joi_1.default.string().required(),
    newPassword: joi_1.default.string().min(8).required(),
});
exports.refreshTokenSchema = joi_1.default.object({
    refreshToken: joi_1.default.string().required(),
});
// Organization schemas
exports.createOrganizationSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).required(),
    slug: joi_1.default.string().min(2).max(50).pattern(/^[a-z0-9-]+$/).required(),
    domain: joi_1.default.string().domain().optional(),
    logo: joi_1.default.string().uri().optional(),
    address: joi_1.default.string().max(255).optional(),
    phone: joi_1.default.string().max(20).optional(),
    email: joi_1.default.string().email().optional(),
    website: joi_1.default.string().uri().optional(),
    timezone: joi_1.default.string().default('UTC'),
    currency: joi_1.default.string().length(3).default('USD'),
    settings: joi_1.default.object().optional(),
});
exports.updateOrganizationSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).optional(),
    slug: joi_1.default.string().min(2).max(50).pattern(/^[a-z0-9-]+$/).optional(),
    domain: joi_1.default.string().domain().allow(null).optional(),
    logo: joi_1.default.string().uri().allow(null).optional(),
    address: joi_1.default.string().max(255).allow(null).optional(),
    phone: joi_1.default.string().max(20).allow(null).optional(),
    email: joi_1.default.string().email().allow(null).optional(),
    website: joi_1.default.string().uri().allow(null).optional(),
    timezone: joi_1.default.string().optional(),
    currency: joi_1.default.string().length(3).optional(),
    settings: joi_1.default.object().allow(null).optional(),
});
// Additional schemas can be added here as needed for future features
