"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseHelper = void 0;
class ResponseHelper {
    static success(res, data, message, statusCode = 200) {
        const response = {
            success: true,
            data,
            message,
            timestamp: new Date().toISOString(),
            path: res.req.originalUrl,
        };
        return res.status(statusCode).json(response);
    }
    static error(res, error, statusCode = 500, message, details) {
        const response = {
            success: false,
            error,
            message,
            statusCode,
            timestamp: new Date().toISOString(),
            path: res.req.originalUrl,
            details,
        };
        return res.status(statusCode).json(response);
    }
    static badRequest(res, message = 'Bad Request', details) {
        return this.error(res, 'BAD_REQUEST', 400, message, details);
    }
    static unauthorized(res, message = 'Unauthorized') {
        return this.error(res, 'UNAUTHORIZED', 401, message);
    }
    static forbidden(res, message = 'Forbidden') {
        return this.error(res, 'FORBIDDEN', 403, message);
    }
    static notFound(res, message = 'Resource not found') {
        return this.error(res, 'NOT_FOUND', 404, message);
    }
    static conflict(res, message = 'Conflict') {
        return this.error(res, 'CONFLICT', 409, message);
    }
    static validationError(res, details, message = 'Validation failed') {
        return this.error(res, 'VALIDATION_ERROR', 422, message, details);
    }
    static internalServerError(res, message = 'Internal server error') {
        return this.error(res, 'INTERNAL_SERVER_ERROR', 500, message);
    }
}
exports.ResponseHelper = ResponseHelper;
