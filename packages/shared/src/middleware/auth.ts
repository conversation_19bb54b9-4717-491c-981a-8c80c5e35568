import { Request, Response, NextFunction } from 'express';
import { J<PERSON><PERSON>elper } from '../utils/jwt';
import { ResponseHelper } from '../utils/response';
import { AuthenticatedRequest } from '../types/api';
import { prisma } from '@crm/database';

export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      ResponseHelper.unauthorized(res, 'Access token required');
      return;
    }

    const decoded = JWTHelper.verifyAccessToken(token);

    // Fetch user from database to ensure they still exist and are active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        organizationId: true,
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
            isActive: true,
          },
        },
      },
    });

    if (!user || !user.isActive) {
      ResponseHelper.unauthorized(res, 'Invalid or inactive user');
      return;
    }

    if (!user.organization || !user.organization.isActive) {
      ResponseHelper.unauthorized(res, 'Organization is inactive');
      return;
    }

    req.user = user;
    next();
  } catch (error) {
    ResponseHelper.unauthorized(res, 'Invalid access token');
  }
};

export const requireRole = (roles: string | string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      ResponseHelper.unauthorized(res, 'Authentication required');
      return;
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(req.user.role)) {
      ResponseHelper.forbidden(res, 'Insufficient permissions');
      return;
    }

    next();
  };
};

export const requireAdmin = requireRole('ADMIN');
export const requireAgent = requireRole(['ADMIN', 'MANAGER', 'AGENT']);

// Organization-based authorization middleware
export const requireOrganizationAccess = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    ResponseHelper.unauthorized(res, 'Authentication required');
    return;
  }

  // Extract organizationId from params or body
  const organizationId = req.params.organizationId || req.body.organizationId;

  if (organizationId && organizationId !== req.user.organizationId) {
    ResponseHelper.forbidden(res, 'Access denied to this organization');
    return;
  }

  next();
};

// Middleware to ensure user can only access their organization's data
export const enforceOrganizationIsolation = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    ResponseHelper.unauthorized(res, 'Authentication required');
    return;
  }

  // Add organizationId to query/body for filtering
  if (req.method === 'GET') {
    req.query.organizationId = req.user.organizationId;
  } else {
    req.body.organizationId = req.user.organizationId;
  }

  next();
};
