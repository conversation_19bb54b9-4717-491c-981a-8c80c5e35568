"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiLimiter = exports.authLimiter = exports.generalLimiter = exports.createRateLimiter = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const response_1 = require("../utils/response");
const createRateLimiter = (options) => {
    return (0, express_rate_limit_1.default)({
        windowMs: options.windowMs || 15 * 60 * 1000, // 15 minutes
        max: options.max || 100, // limit each IP to 100 requests per windowMs
        skipSuccessfulRequests: options.skipSuccessfulRequests || false,
        handler: (req, res) => {
            response_1.ResponseHelper.error(res, 'RATE_LIMIT_EXCEEDED', 429, options.message || 'Too many requests, please try again later');
        },
        standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
        legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    });
};
exports.createRateLimiter = createRateLimiter;
// Pre-configured rate limiters
exports.generalLimiter = (0, exports.createRateLimiter)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 300, // 100 requests per 15 minutes
});
exports.authLimiter = (0, exports.createRateLimiter)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 login attempts per 15 minutes
    message: 'Too many authentication attempts, please try again later',
    skipSuccessfulRequests: true,
});
exports.apiLimiter = (0, exports.createRateLimiter)({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 60, // 60 requests per minute
});
