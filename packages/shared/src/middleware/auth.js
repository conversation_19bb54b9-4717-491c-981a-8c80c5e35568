"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.enforceOrganizationIsolation = exports.requireOrganizationAccess = exports.requireAgent = exports.requireAdmin = exports.requireRole = exports.authenticateToken = void 0;
const jwt_1 = require("../utils/jwt");
const response_1 = require("../utils/response");
const database_1 = require("@crm/database");
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
        if (!token) {
            response_1.ResponseHelper.unauthorized(res, 'Access token required');
            return;
        }
        const decoded = jwt_1.JWTHelper.verifyAccessToken(token);
        // Fetch user from database to ensure they still exist and are active
        const user = await database_1.prisma.user.findUnique({
            where: { id: decoded.userId },
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                role: true,
                isActive: true,
                organizationId: true,
                organization: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                        isActive: true,
                    },
                },
            },
        });
        if (!user || !user.isActive) {
            response_1.ResponseHelper.unauthorized(res, 'Invalid or inactive user');
            return;
        }
        if (!user.organization || !user.organization.isActive) {
            response_1.ResponseHelper.unauthorized(res, 'Organization is inactive');
            return;
        }
        req.user = user;
        next();
    }
    catch (error) {
        response_1.ResponseHelper.unauthorized(res, 'Invalid access token');
    }
};
exports.authenticateToken = authenticateToken;
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            response_1.ResponseHelper.unauthorized(res, 'Authentication required');
            return;
        }
        const allowedRoles = Array.isArray(roles) ? roles : [roles];
        if (!allowedRoles.includes(req.user.role)) {
            response_1.ResponseHelper.forbidden(res, 'Insufficient permissions');
            return;
        }
        next();
    };
};
exports.requireRole = requireRole;
exports.requireAdmin = (0, exports.requireRole)('ADMIN');
exports.requireAgent = (0, exports.requireRole)(['ADMIN', 'MANAGER', 'AGENT']);
// Organization-based authorization middleware
const requireOrganizationAccess = (req, res, next) => {
    if (!req.user) {
        response_1.ResponseHelper.unauthorized(res, 'Authentication required');
        return;
    }
    // Extract organizationId from params or body
    const organizationId = req.params.organizationId || req.body.organizationId;
    if (organizationId && organizationId !== req.user.organizationId) {
        response_1.ResponseHelper.forbidden(res, 'Access denied to this organization');
        return;
    }
    next();
};
exports.requireOrganizationAccess = requireOrganizationAccess;
// Middleware to ensure user can only access their organization's data
const enforceOrganizationIsolation = (req, res, next) => {
    if (!req.user) {
        response_1.ResponseHelper.unauthorized(res, 'Authentication required');
        return;
    }
    // Add organizationId to query/body for filtering
    if (req.method === 'GET') {
        req.query.organizationId = req.user.organizationId;
    }
    else {
        req.body.organizationId = req.user.organizationId;
    }
    next();
};
exports.enforceOrganizationIsolation = enforceOrganizationIsolation;
