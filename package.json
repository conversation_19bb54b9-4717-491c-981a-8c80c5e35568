{"name": "real-estate-crm", "version": "1.0.0", "description": "A comprehensive CRM application for real estate management", "private": true, "workspaces": ["services/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:gateway\" \"npm run dev:auth\" \"npm run dev:organizations\"", "dev:gateway": "npm run dev --workspace=services/api-gateway", "dev:auth": "npm run dev --workspace=services/auth-service", "dev:organizations": "npm run dev --workspace=services/organizations-service", "test": "npm run test:unit && npm run test:integration", "test:unit": "npm run test:unit:auth && npm run test:unit:organizations", "test:integration": "npm run test:integration:auth && npm run test:integration:organizations && npm run test:integration:api", "test:unit:auth": "npm run test:unit --workspace=services/auth-service", "test:unit:organizations": "npm run test:unit --workspace=services/organizations-service", "test:integration:auth": "npm run test:integration --workspace=services/auth-service", "test:integration:organizations": "npm run test:integration --workspace=services/organizations-service", "test:integration:api": "jest tests/integration --testTimeout=30000", "test:coverage": "npm run test:coverage --workspace=services/auth-service && npm run test:coverage --workspace=services/organizations-service", "test:watch": "npm run test:watch --workspace=services/auth-service", "build": "npm run build --workspaces", "start": "npm run start --workspaces", "db:generate": "npm run db:generate --workspace=packages/database", "db:push": "npm run db:push --workspace=packages/database", "db:migrate": "npm run db:migrate --workspace=packages/database", "db:studio": "npm run db:studio --workspace=packages/database", "db:seed": "npm run db:seed --workspace=packages/database", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "install:all": "npm install && npm install --workspaces"}, "devDependencies": {"@types/node": "^20.0.0", "@types/supertest": "^6.0.3", "concurrently": "^8.2.0", "nodemon": "^3.0.0", "supertest": "^7.1.1", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["crm", "real-estate", "microservices", "nodejs", "express", "postgresql", "redis"], "author": "Real Estate CRM Team", "license": "MIT", "dependencies": {"axios": "^1.9.0"}}