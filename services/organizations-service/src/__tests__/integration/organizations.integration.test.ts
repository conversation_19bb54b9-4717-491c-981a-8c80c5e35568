import request from 'supertest';
import app from '../../index';
import { testDb, setupTestData, cleanupTestData } from './setup';
import { JWTHelper } from '@crm/shared';

describe('Organizations Integration Tests', () => {
  let testData: any;
  let superAdminToken: string;
  let adminToken: string;
  let agentToken: string;
  let anotherOrgUserToken: string;

  beforeAll(async () => {
    testData = await setupTestData();

    // Generate tokens for different users
    superAdminToken = JWTHelper.generateTokenPair({
      userId: testData.superAdmin.id,
      email: testData.superAdmin.email,
      role: testData.superAdmin.role,
      organizationId: testData.superAdmin.organizationId,
    }).accessToken;

    adminToken = JWTHelper.generateTokenPair({
      userId: testData.admin.id,
      email: testData.admin.email,
      role: testData.admin.role,
      organizationId: testData.admin.organizationId,
    }).accessToken;

    agentToken = JWTHelper.generateTokenPair({
      userId: testData.agent.id,
      email: testData.agent.email,
      role: testData.agent.role,
      organizationId: testData.agent.organizationId,
    }).accessToken;

    anotherOrgUserToken = JWTHelper.generateTokenPair({
      userId: testData.userFromAnotherOrg.id,
      email: testData.userFromAnotherOrg.email,
      role: testData.userFromAnotherOrg.role,
      organizationId: testData.userFromAnotherOrg.organizationId,
    }).accessToken;
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  describe('GET /', () => {
    it('should successfully get user organizations', async () => {
      const response = await request(app)
        .get('/')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toMatchObject({
        id: testData.testOrganization.id,
        name: 'Test Organization',
        slug: 'test-org',
      });
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should return different organization for different user', async () => {
      const response = await request(app)
        .get('/')
        .set('Authorization', `Bearer ${anotherOrgUserToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toMatchObject({
        id: testData.anotherOrganization.id,
        name: 'Another Organization',
        slug: 'another-org',
      });
    });
  });

  describe('GET /:id', () => {
    it('should successfully get organization by ID', async () => {
      const response = await request(app)
        .get(`/${testData.testOrganization.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        id: testData.testOrganization.id,
        name: 'Test Organization',
        slug: 'test-org',
        email: '<EMAIL>',
      });
    });

    it('should fail when accessing different organization', async () => {
      const response = await request(app)
        .get(`/${testData.anotherOrganization.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Access denied to this organization');
    });

    it('should fail with nonexistent organization ID', async () => {
      const response = await request(app)
        .get('/nonexistent-org-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Organization not found');
    });
  });

  describe('POST /', () => {
    it('should successfully create organization as super admin', async () => {
      const newOrgData = {
        name: 'New Test Organization',
        slug: 'new-test-org',
        email: '<EMAIL>',
        phone: '******-1111',
        timezone: 'UTC',
        currency: 'USD',
      };

      const response = await request(app)
        .post('/')
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send(newOrgData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        name: 'New Test Organization',
        slug: 'new-test-org',
        email: '<EMAIL>',
      });

      // Verify organization was created in database
      const createdOrg = await testDb.organization.findUnique({
        where: { slug: 'new-test-org' },
      });
      expect(createdOrg).toBeTruthy();
    });

    it('should fail to create organization as regular admin', async () => {
      const newOrgData = {
        name: 'Another New Organization',
        slug: 'another-new-org',
        timezone: 'UTC',
        currency: 'USD',
      };

      const response = await request(app)
        .post('/')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newOrgData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Only super admins can create organizations');
    });

    it('should fail to create organization with duplicate slug', async () => {
      const duplicateOrgData = {
        name: 'Duplicate Organization',
        slug: 'test-org', // This slug already exists
        timezone: 'UTC',
        currency: 'USD',
      };

      const response = await request(app)
        .post('/')
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send(duplicateOrgData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Organization slug already exists');
    });

    it('should fail with invalid data', async () => {
      const invalidOrgData = {
        name: 'A', // Too short
        slug: 'invalid-slug-with-spaces and-special-chars!',
        timezone: 'UTC',
        currency: 'USD',
      };

      const response = await request(app)
        .post('/')
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send(invalidOrgData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /:id', () => {
    it('should successfully update organization as admin', async () => {
      const updateData = {
        name: 'Updated Test Organization',
        phone: '******-2222',
        website: 'https://updated-test.com',
      };

      const response = await request(app)
        .put(`/${testData.testOrganization.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        name: 'Updated Test Organization',
        phone: '******-2222',
        website: 'https://updated-test.com',
      });

      // Verify organization was updated in database
      const updatedOrg = await testDb.organization.findUnique({
        where: { id: testData.testOrganization.id },
      });
      expect(updatedOrg?.name).toBe('Updated Test Organization');
    });

    it('should successfully update organization as super admin', async () => {
      const updateData = {
        address: '456 Updated St',
      };

      const response = await request(app)
        .put(`/${testData.testOrganization.id}`)
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.address).toBe('456 Updated St');
    });

    it('should fail to update organization as agent', async () => {
      const updateData = {
        name: 'Agent Updated Name',
      };

      const response = await request(app)
        .put(`/${testData.testOrganization.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .send(updateData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Insufficient permissions to update organization');
    });

    it('should fail to update different organization', async () => {
      const updateData = {
        name: 'Hacked Name',
      };

      const response = await request(app)
        .put(`/${testData.anotherOrganization.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Access denied to this organization');
    });
  });

  describe('DELETE /:id', () => {
    let organizationToDelete: any;

    beforeEach(async () => {
      // Create a new organization for deletion tests
      organizationToDelete = await testDb.organization.create({
        data: {
          name: 'Organization To Delete',
          slug: 'org-to-delete',
          timezone: 'UTC',
          currency: 'USD',
        },
      });

      // Create a user for this organization
      const hashedPassword = await testDb.user.findUnique({
        where: { id: testData.admin.id },
        select: { password: true },
      });

      await testDb.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword!.password,
          firstName: 'Delete',
          lastName: 'Admin',
          organizationId: organizationToDelete.id,
          role: 'ADMIN',
          isVerified: true,
        },
      });
    });

    afterEach(async () => {
      // Clean up
      await testDb.user.deleteMany({
        where: { email: '<EMAIL>' },
      });
      await testDb.organization.deleteMany({
        where: { slug: 'org-to-delete' },
      });
    });

    it('should successfully soft delete organization as admin', async () => {
      // Generate token for the admin of the organization to be deleted
      const deleteAdminToken = JWTHelper.generateTokenPair({
        userId: 'temp-user-id',
        email: '<EMAIL>',
        role: 'ADMIN',
        organizationId: organizationToDelete.id,
      }).accessToken;

      const response = await request(app)
        .delete(`/${organizationToDelete.id}`)
        .set('Authorization', `Bearer ${deleteAdminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Organization deleted successfully');

      // Verify organization was soft deleted
      const deletedOrg = await testDb.organization.findUnique({
        where: { id: organizationToDelete.id },
      });
      expect(deletedOrg?.isDeleted).toBe(true);
      expect(deletedOrg?.isActive).toBe(false);
      expect(deletedOrg?.deletedAt).toBeTruthy();
    });

    it('should fail to delete organization as agent', async () => {
      const response = await request(app)
        .delete(`/${testData.testOrganization.id}`)
        .set('Authorization', `Bearer ${agentToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Insufficient permissions to delete organization');
    });

    it('should fail to delete different organization', async () => {
      const response = await request(app)
        .delete(`/${organizationToDelete.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Access denied to this organization');
    });
  });
});
