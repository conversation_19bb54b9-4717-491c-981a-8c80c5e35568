{"name": "@crm/organizations-service", "version": "1.0.0", "description": "Organizations microservice for Real Estate CRM", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "nodemon src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "clean": "rm -rf dist"}, "dependencies": {"@crm/shared": "*", "@crm/database": "*", "express": "^4.18.0", "dotenv": "^16.3.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/node": "^20.0.0", "typescript": "^5.0.0", "nodemon": "^3.0.0", "ts-node": "^10.9.0", "jest": "^29.6.0", "@types/jest": "^29.5.0", "ts-jest": "^29.1.0"}}