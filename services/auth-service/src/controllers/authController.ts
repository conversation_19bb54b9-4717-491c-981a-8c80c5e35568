import { Response } from 'express';
import {
  AuthenticatedRequest,
  ResponseHelper,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  PasswordHelper,
  redisClient,
  createServiceLogger,
} from '@crm/shared';
import { prisma } from '@crm/database';
import * as authService from '../services/authService';

const logger = createServiceLogger('auth-controller');

export const login = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { email, password } = req.body;

    const result = await authService.loginUser(email, password);

    if (!result.success) {
      ResponseHelper.unauthorized(res, result.message);
      return;
    }

    logger.info(`User logged in: ${email}`);
    ResponseHelper.success(res, result.data, 'Login successful');
  } catch (error) {
    logger.error('Login error:', error);
    ResponseHelper.internalServerError(res, 'Login failed');
  }
};

export const register = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userData = req.body;

    const result = await authService.registerUser(userData);

    if (!result.success) {
      ResponseHelper.badRequest(res, result.message);
      return;
    }

    logger.info(`User registered: ${userData.email}`);
    ResponseHelper.success(res, result.data, 'User registered successfully', 201);
  } catch (error) {
    logger.error('Registration error:', error);
    ResponseHelper.internalServerError(res, 'Registration failed');
  }
};

export const refreshToken = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { refreshToken } = req.body;

    const result = await authService.refreshUserToken(refreshToken);

    if (!result.success) {
      ResponseHelper.unauthorized(res, result.message);
      return;
    }

    ResponseHelper.success(res, result.data, 'Token refreshed successfully');
  } catch (error) {
    logger.error('Token refresh error:', error);
    ResponseHelper.internalServerError(res, 'Token refresh failed');
  }
};

export const logout = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      // Add token to blacklist in Redis
      const decoded = JWTHelper.verifyAccessToken(token);
      const expiresIn = decoded.exp! - Math.floor(Date.now() / 1000);

      if (expiresIn > 0) {
        await redisClient.set(`blacklist:${token}`, 'true', expiresIn);
      }
    }

    logger.info(`User logged out: ${req.user?.email}`);
    ResponseHelper.success(res, null, 'Logout successful');
  } catch (error) {
    logger.error('Logout error:', error);
    ResponseHelper.internalServerError(res, 'Logout failed');
  }
};

export const getCurrentUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
            isActive: true,
          },
        },
      },
    });

    if (!user) {
      ResponseHelper.notFound(res, 'User not found');
      return;
    }

    ResponseHelper.success(res, user);
  } catch (error) {
    logger.error('Get current user error:', error);
    ResponseHelper.internalServerError(res, 'Failed to get user information');
  }
};

export const changePassword = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user!.id;

    const result = await authService.changeUserPassword(userId, currentPassword, newPassword);

    if (!result.success) {
      ResponseHelper.badRequest(res, result.message);
      return;
    }

    logger.info(`Password changed for user: ${req.user?.email}`);
    ResponseHelper.success(res, null, 'Password changed successfully');
  } catch (error) {
    logger.error('Change password error:', error);
    ResponseHelper.internalServerError(res, 'Password change failed');
  }
};
